// 广告弹窗杀手 - 后台脚本
class BackgroundService {
    constructor() {
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手后台服务已启动');
        
        // 监听插件安装
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('广告弹窗杀手插件已安装');
                this.showWelcomeNotification();
            }
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.injectContentScript(tabId);
            }
        });
    }

    async injectContentScript(tabId) {
        try {
            // 检查是否可以注入脚本
            const tab = await chrome.tabs.get(tabId);
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }
            
            // 注入内容脚本（如果还没有注入）
            await chrome.scripting.executeScript({
                target: {tabId: tabId},
                files: ['内容脚本.js']
            });
            
        } catch (error) {
            // 忽略注入失败的情况（可能是权限问题）
            console.log('无法注入内容脚本到标签页:', tabId, error.message);
        }
    }

    showWelcomeNotification() {
        // 可以在这里添加欢迎通知逻辑
        console.log('欢迎使用广告弹窗杀手！');
    }
}

// 创建后台服务实例
const backgroundService = new BackgroundService();
